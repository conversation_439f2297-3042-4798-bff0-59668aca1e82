# Localhost Handling & Crash Recovery

This document outlines the improved localhost handling system that ensures the development server runs specifically on port 8085 and provides robust crash recovery mechanisms.

## 🎯 Key Features

### ✅ Port Management
- **Primary Port**: Always attempts to use port 8085 (not 8086 or others)
- **Intelligent Fallback**: Only uses alternative ports (8086-8090) when 8085 is unavailable
- **Port Monitoring**: Continuously monitors server health and port availability
- **Smart Detection**: Distinguishes between intended shutdowns and crashes

### 🛡️ Crash Recovery
- **Automatic Detection**: Monitors server state and detects unexpected crashes
- **Retry Logic**: Attempts recovery up to 3 times before requiring manual intervention
- **Port Restoration**: Always tries to restore service on port 8085 after crashes
- **User Notifications**: Provides clear guidance for manual recovery when needed

### 🔧 Development Tools
- **Server Status Component**: Real-time server health monitoring
- **Port Checking Utilities**: Manual port availability testing
- **Development Panel**: Debugging tools for server issues (development mode only)
- **Smart Startup Scripts**: Enhanced server startup with conflict resolution

## 🚀 Usage

### Basic Development Server
```bash
# Standard Vite development server (port 8085)
npm run dev

# Smart development server with crash recovery
npm run dev:smart

# Safe startup (kills existing processes first)
npm run dev:safe
```

### Port-Specific Commands
```bash
# Force start on specific port
npm run dev -- --port 8085

# Smart start with cleanup
node scripts/start-dev.js --kill-existing

# Force specific port with smart start
node scripts/start-dev.js --port=8085
```

### Manual Port Checking
```javascript
import { checkDefaultPortOnly, logPortStatus } from '@/utils/portManager';

// Check if port 8085 is available
const status = await checkDefaultPortOnly();
console.log(status.available ? 'Port 8085 ready' : 'Port 8085 busy');

// Detailed port status logging
await logPortStatus();
```

## 🔍 Components

### ServerStatus Component
Provides real-time server monitoring with visual indicators:

```tsx
import { ServerStatus, ServerStatusIndicator } from '@/components/ServerStatus';

// Full status panel
<ServerStatus showDetails={true} />

// Minimal indicator for headers
<ServerStatusIndicator />
```

### Server Development Tools
Development-only panel for debugging server issues:

```tsx
import { ServerDevelopmentTools } from '@/components/ServerStatus';

// Automatically appears in development mode
<ServerDevelopmentTools />
```

## ⚙️ Configuration

### Vite Configuration
The `vite.config.ts` has been enhanced with:

```typescript
server: {
  host: "::",
  port: 8085, // Primary port - do not change to 8086 or others
  strictPort: false, // Allow fallback to other ports if 8085 is busy
  open: false, // Prevent auto-opening browser to control port opening
  hmr: {
    overlay: true, // Show error overlay on crashes
  },
  watch: {
    usePolling: false,
    interval: 1000,
  },
}
```

### Port Manager Settings
Configure port behavior in `src/utils/portManager.ts`:

```typescript
const DEFAULT_PORT = 8085;
const FALLBACK_PORTS = [8086, 8087, 8088, 8089, 8090];
const MAX_RETRIES = 3;
```

## 🔔 Server State Monitoring

### React Hook
Use the `useServerState` hook to monitor server health:

```tsx
import { useServerState } from '@/utils/portManager';

function MyComponent() {
  const serverState = useServerState();
  
  return (
    <div>
      Status: {serverState.crashed ? 'Crashed' : 'Running'}
      Port: {serverState.port}
      Retries: {serverState.retryCount}
    </div>
  );
}
```

### Event Handling
Listen for server events:

```javascript
// Server crash detected
window.addEventListener('server-crash', (event) => {
  console.log('Server crashed:', event.detail);
});

// Critical crash requiring manual intervention
window.addEventListener('server-critical-crash', (event) => {
  console.error('Manual restart required:', event.detail);
});
```

## 🚨 Troubleshooting

### Common Issues

**Port 8085 Always Busy**
```bash
# Check what's using the port (Windows)
netstat -ano | findstr :8085

# Check what's using the port (macOS/Linux)
lsof -i :8085

# Use safe startup to kill existing processes
npm run dev:safe
```

**Server Keeps Crashing**
1. Check the server status panel for crash count
2. Look for error messages in the console
3. Try manual restart after 3 automatic attempts
4. Use `npm run dev:safe` to clear port conflicts

**Wrong Port Detected**
- Server Status component will show "Non-preferred Port" warning
- Check if port 8085 is available
- Restart server to attempt port 8085 again

### Manual Recovery
When automatic recovery fails:

```bash
# Step 1: Kill all Node.js processes (be careful!)
taskkill /f /im node.exe  # Windows
killall node              # macOS/Linux

# Step 2: Clear port 8085 specifically
# Windows:
for /f "tokens=5" %a in ('netstat -aon ^| find ":8085"') do taskkill /f /pid %a

# macOS/Linux:
lsof -ti:8085 | xargs kill -9

# Step 3: Restart with safe mode
npm run dev:safe
```

## 🏗️ Architecture

### File Structure
```
src/
├── utils/
│   └── portManager.ts           # Core port management logic
├── components/
│   └── ServerStatus.tsx         # UI components for server monitoring
└── App.tsx                      # Integrated server monitoring

scripts/
└── start-dev.js                 # Smart development server starter

vite.config.ts                   # Enhanced Vite configuration
package.json                     # Updated scripts
```

### Key Classes
- `ServerStateManager`: Monitors server health and handles crash recovery
- `PortStatus`: Represents port availability information
- `ServerState`: Current server status and metrics

## 🔮 Future Enhancements

- **Multi-Project Detection**: Detect other Vite projects using port 8085
- **Process Management**: Better process lifecycle management
- **Health Metrics**: Detailed performance monitoring
- **Auto-Recovery**: More intelligent crash recovery strategies
- **Port Reservation**: Reserve port 8085 for this project specifically

## 📝 Notes

- Server monitoring only runs in development mode
- Port checking uses lightweight HTTP requests
- Crash detection has a 30-second check interval by default
- All port operations are non-blocking and timeout after 2 seconds

This system ensures that your development environment runs reliably on port 8085 and provides clear feedback when issues arise, making debugging and recovery much easier.
