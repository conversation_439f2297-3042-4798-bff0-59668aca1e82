import { useState, useCallback, useEffect, useRef } from 'react';
import * as THREE from 'three';
import { SimpleNode, VisualConnection } from './types'; // Assuming types are in a shared file

export interface ForceDirectedLayoutOptions {
  repulsionStrength: number;
  attractionStrength: number;
  restLength: number;
  dampingFactor: number;
  maxSpeed: number;
  centerForceStrength: number;
  iterationsPerFrame?: number; // Number of simulation steps per animation frame
}

export const useForceDirectedLayout = (
  nodes: Map<string, SimpleNode>,
  connections: Map<string, VisualConnection>,
  options: ForceDirectedLayoutOptions
) => {
  const [isRunning, setIsRunning] = useState(true);
  const optionsRef = useRef(options);
  const frameCountRef = useRef(0);

  useEffect(() => {
    optionsRef.current = options;
  }, [options]);

  const step = useCallback((draggedNodeId: string | null) => {
    if (!nodes || nodes.size === 0) return;

    // Performance optimization: limit iterations for large datasets
    const nodeCount = nodes.size;
    let maxIterations = optionsRef.current.iterationsPerFrame || 1;
    
    // Reduce iterations for large datasets to prevent freezing
    if (nodeCount > 100) maxIterations = 1;
    if (nodeCount > 200) {
      frameCountRef.current++;
      // Skip every other frame for very large datasets
      if (frameCountRef.current % 2 === 0) return;
    }

    const currentOptions = optionsRef.current;

    for (let iter = 0; iter < maxIterations; iter++) {
      // 1. Reset forces
      nodes.forEach(node => {
        if (!node.force) node.force = new THREE.Vector2();
        node.force.set(0, 0);
      });      // 2. Calculate Repulsion Forces with optimizations
      const nodeList = Array.from(nodes.values());
      const maxRepulsionDistance = 100; // Skip repulsion beyond this distance
      
      for (let i = 0; i < nodeList.length; i++) {
        const nodeA = nodeList[i];
        if (nodeA.id === draggedNodeId || nodeA.isDragged) continue;

        // Performance optimization: only check nearby nodes for large datasets
        const maxChecks = nodeCount > 50 ? Math.min(20, nodeList.length) : nodeList.length;
        const startJ = nodeCount > 100 ? Math.max(0, i - 10) : i + 1;
        const endJ = nodeCount > 100 ? Math.min(nodeList.length, i + 10) : nodeList.length;

        for (let j = startJ; j < endJ && j - i < maxChecks; j++) {
          if (j === i) continue;
          const nodeB = nodeList[j];
          if (nodeB.id === draggedNodeId || nodeB.isDragged) continue;

          const deltaX = nodeA.position.x - nodeB.position.x;
          const deltaY = nodeA.position.y - nodeB.position.y;
          const distanceSq = deltaX * deltaX + deltaY * deltaY;
          
          // Early exit for distant nodes
          if (distanceSq > maxRepulsionDistance * maxRepulsionDistance) continue;
          
          const distance = Math.sqrt(distanceSq);

          if (distance > 0.1) { // Minimum distance threshold
            const effectiveRepulsion = Math.max(0.001, currentOptions.repulsionStrength);
            const repulsiveForce = (effectiveRepulsion * 0.1) / distanceSq; 
            const forceX = (deltaX / distance) * repulsiveForce;
            const forceY = (deltaY / distance) * repulsiveForce;
            
            nodeA.force.x += forceX;
            nodeA.force.y += forceY;
            nodeB.force.x -= forceX;
            nodeB.force.y -= forceY;
          } else {
            // Add a small random force if nodes are too close
            const randomX = (Math.random() - 0.5) * 0.1;
            const randomY = (Math.random() - 0.5) * 0.1;
            nodeA.force.x += randomX;
            nodeA.force.y += randomY;
            nodeB.force.x -= randomX;
            nodeB.force.y -= randomY;
          }
        }
      }      // 3. Calculate Attraction Forces (Springs) - optimized
      connections.forEach(conn => {
        const sourceNode = nodes.get(conn.sourceNode.id);
        const targetNode = nodes.get(conn.targetNode.id);

        if (sourceNode && targetNode) {
          const deltaX = targetNode.position.x - sourceNode.position.x;
          const deltaY = targetNode.position.y - sourceNode.position.y;
          const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
          
          if (distance > 0.1) { // Avoid division by zero
            const displacement = distance - currentOptions.restLength;
            const attractiveForce = currentOptions.attractionStrength * displacement;
            const forceX = (deltaX / distance) * attractiveForce;
            const forceY = (deltaY / distance) * attractiveForce;

            if (sourceNode.id !== draggedNodeId && !sourceNode.isDragged) {
              sourceNode.force.x += forceX;
              sourceNode.force.y += forceY;
            }
            if (targetNode.id !== draggedNodeId && !targetNode.isDragged) {
              targetNode.force.x -= forceX;
              targetNode.force.y -= forceY;
            }
          }
        }
      });
        // 4. Calculate Center Force - optimized
      if (currentOptions.centerForceStrength > 0) {
        const effectiveCenterForce = Math.max(0, currentOptions.centerForceStrength) * 0.01;
        nodeList.forEach(node => {
          if (node.id === draggedNodeId || node.isDragged) return;
          node.force.x -= node.position.x * effectiveCenterForce;
          node.force.y -= node.position.y * effectiveCenterForce;
        });
      }

      // 5. Update velocities and positions (Euler integration) - optimized
      let totalMovement = 0; // Track convergence
      nodes.forEach(node => {
        if (node.id === draggedNodeId || node.isDragged) {
          if (node.velocity) {
            node.velocity.x = 0;
            node.velocity.y = 0;
          }
          return;
        }
        
        if (!node.velocity) node.velocity = new THREE.Vector2();
        
        // Update velocity with damping
        node.velocity.x = (node.velocity.x + node.force.x) * currentOptions.dampingFactor;
        node.velocity.y = (node.velocity.y + node.force.y) * currentOptions.dampingFactor;

        // Clamp speed to maxSpeed
        const velocityMagnitude = Math.sqrt(node.velocity.x * node.velocity.x + node.velocity.y * node.velocity.y);
        if (velocityMagnitude > currentOptions.maxSpeed) {
          const scale = currentOptions.maxSpeed / velocityMagnitude;
          node.velocity.x *= scale;
          node.velocity.y *= scale;
        }

        // Update position
        node.position.x += node.velocity.x;
        node.position.y += node.velocity.y;
        
        // Track total movement for convergence detection
        totalMovement += velocityMagnitude;
        
        // Update the Three.js mesh position (batch this for better performance)
        if (node.mesh) {
          node.mesh.position.set(node.position.x, node.position.y, 0);
        }
      });

      // Early termination if system has converged
      if (totalMovement < 0.01 * nodeCount) {
        break; // Exit iterations early if movement is minimal
      }
    }

  }, [nodes, connections, optionsRef]); // dependencies for useCallback

  return { step, isRunning, setIsRunning, config: optionsRef.current };
};
