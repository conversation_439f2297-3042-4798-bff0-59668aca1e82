
# Insights Discovery Feature - Architecture Diagram Specifications

## Visual Diagram Layout Guide for Figma/Design Tools

### Diagram Style Guidelines
- **Modern SaaS Architecture Style**: Clean, minimal, professional
- **Color Coding**:
  - UI Components: Light Blue (#E3F2FD)
  - Services: Orange (#FFF3E0)
  - Data Stores: Green (#E8F5E8)
- **Shapes**:
  - UI Components: Rounded rectangles
  - Services: Hexagons
  - Data Stores: Cylinders/Database icons
- **Layout**: Top-to-bottom flow (UI → Services → Data)

## Component Hierarchy (Visual Structure)

```
AnalysisResultsView (Main Container)
├── AnswerCard (Multiple instances)
│   ├── Answer Content Display
│   ├── AddNoteButton
│   └── Existing Actions (Save, Copy, etc.)
├── NoteEditorModal (Overlay/Modal)
│   ├── Note Text Input
│   ├── Link Selection Interface
│   ├── Save Button
│   └── Cancel Button
└── Continue Analysis Button

LibraryView (Separate Section)
└── InsightsCanvas (New Component)
    ├── Notes List
    ├── Note Card Display
    └── Discovery Action Buttons
```

## Data Flow Steps (Numbered for Diagram Arrows)

### Step 1: Initial Data Population
**From**: AI Analysis Cache (Session Storage)
**To**: AnalysisResultsView
**Action**: Load and display answer cards

### Step 2: User Interaction - Add Note
**From**: User clicks AddNoteButton on AnswerCard
**To**: NoteEditorModal
**Action**: Modal opens with context (question_id, answer_id)

### Step 3: Modal Context Setup
**From**: AnswerCard
**To**: NoteEditorModal
**Data**: {question_id, answer_id, answer_content}

### Step 4: Note Creation & Linking
**From**: User interaction in NoteEditorModal
**To**: Background AnswerCards selection
**Action**: User types note and selects additional linked answers

### Step 5: Save Note Action
**From**: NoteEditorModal Save Button
**To**: LibraryService
**Data**: {note_text, question_id, linked_answer_ids: [...], timestamp}

### Step 6: Persistent Storage
**From**: LibraryService
**To**: Library Database (LocalStorage)
**Action**: Save note to User Notes collection

### Step 7: Navigate to Library
**From**: User navigation
**To**: LibraryView → InsightsCanvas
**Action**: User opens insights section

### Step 8: Load Saved Notes
**From**: InsightsCanvas
**To**: LibraryService
**Action**: Fetch all saved notes from database

### Step 9: Discovery Action
**From**: User clicks note in InsightsCanvas
**To**: AnalysisResultsView (Context Recreation)
**Action**: Load original question + highlight linked answers

## Component Specifications for Implementation

### New Components Needed:

#### 1. AddNoteButton
- **Location**: Inside each AnswerCard
- **Props**: {questionId, answerId, answerContent}
- **Styling**: Small icon button with note/insight icon

#### 2. NoteEditorModal
- **Props**: {isOpen, onClose, questionId, answerId, answerContent, allAnswers}
- **Features**:
  - Rich text editor for note
  - Multi-select interface for linking other answers
  - Preview of linked answers
  - Save/Cancel actions

#### 3. InsightsCanvas
- **Location**: New tab/section in LibraryView
- **Features**:
  - Grid/list view of saved notes
  - Search and filter capabilities
  - Note preview cards
  - Discovery action buttons

### Enhanced Services:

#### LibraryService Extensions
```typescript
// New methods to add:
- saveNote(note: UserNote): void
- getNotes(): UserNote[]
- deleteNote(noteId: string): void
- getNotesForQuestion(questionId: string): UserNote[]
- searchNotes(query: string): UserNote[]
```

### New Data Types:

#### UserNote Interface
```typescript
interface UserNote {
  id: string;
  questionId: string;
  noteText: string;
  linkedAnswerIds: string[];
  createdAt: Date;
  updatedAt: Date;
  tags?: string[];
}
```

## Visual Diagram Layout Instructions

### Top Level (UI Layer)
1. **AnalysisResultsView** - Large container at top
2. **AnswerCard components** - Multiple cards within container
3. **NoteEditorModal** - Overlay/popup (shown with dashed lines to indicate modal)
4. **LibraryView** - Separate section on right or below
5. **InsightsCanvas** - Contained within LibraryView

### Middle Level (Services Layer)
1. **LibraryService** - Central hexagon shape
2. Connection lines to both UI components above and data stores below

### Bottom Level (Data Layer)
1. **AI Analysis Cache** - Cylinder shape (Session Storage)
2. **Library Database** - Cylinder shape (Persistent Storage)
3. **User Notes Collection** - Nested within Library Database

### Arrow Flows
- Use numbered arrows (1-9) following the data flow steps
- Different arrow styles:
  - Solid arrows for data flow
  - Dashed arrows for user interactions
  - Double arrows for bi-directional data flow

### Additional Visual Elements
- **User icon** at the top to show user interactions
- **Cloud icon** to represent session storage
- **Database icon** for persistent storage
- **Modal overlay** effect for NoteEditorModal
- **Highlight boxes** around linked components during discovery flow

## Implementation Priority Order
1. UserNote type definitions
2. LibraryService extensions
3. AddNoteButton component
4. NoteEditorModal component
5. InsightsCanvas component
6. Integration with existing AnalysisResults
7. Discovery flow implementation

This specification provides the complete blueprint for creating a professional SaaS-style architecture diagram in Figma or similar design tools.
