/**
 * Port Manager Test Utility
 * Quick test script to verify port management functionality
 */

import { 
  checkPortAvailability, 
  checkDefaultPortOnly, 
  findAvailablePort,
  isRunningOnPreferredPort,
  getCurrentPort,
  logPortStatus
} from '../src/utils/portManager';

async function runPortTests() {
  console.log('🧪 Port Manager Test Suite');
  console.log('==========================');

  try {
    // Test 1: Check default port
    console.log('\n1️⃣ Testing default port check...');
    const defaultStatus = await checkDefaultPortOnly();
    console.log(`Port 8085: ${defaultStatus.available ? '✅ Available' : '❌ In use'}`);
    if (defaultStatus.error) {
      console.log(`   Error: ${defaultStatus.error}`);
    }

    // Test 2: Check specific ports
    console.log('\n2️⃣ Testing specific port checks...');
    const testPorts = [8085, 8086, 8087, 3000, 5173];
    for (const port of testPorts) {
      const status = await checkPortAvailability(port);
      console.log(`Port ${port}: ${status.available ? '✅ Available' : '❌ In use'}`);
    }

    // Test 3: Find available port
    console.log('\n3️⃣ Testing available port finder...');
    const availablePort = await findAvailablePort();
    console.log(`Found available port: ${availablePort.port} (${availablePort.available ? '✅' : '❌'})`);
    if (availablePort.error) {
      console.log(`   Error: ${availablePort.error}`);
    }

    // Test 4: Current environment checks
    console.log('\n4️⃣ Testing environment checks...');
    if (typeof window !== 'undefined') {
      const currentPort = getCurrentPort();
      const isPreferred = isRunningOnPreferredPort();
      console.log(`Current port: ${currentPort}`);
      console.log(`Is preferred port: ${isPreferred ? '✅ Yes' : '⚠️ No'}`);
    } else {
      console.log('Running in Node.js environment - window checks skipped');
    }

    // Test 5: Detailed port status
    console.log('\n5️⃣ Detailed port status...');
    await logPortStatus();

    console.log('\n✅ All tests completed successfully!');

  } catch (error) {
    console.error('\n❌ Test failed:', error);
  }
}

// Export for use in other test files
export { runPortTests };

// Run tests if this file is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  runPortTests();
}
