/**
 * Port Management Utility
 * Handles localhost port checking and crash recovery for development server
 */

import React from 'react';

const DEFAULT_PORT = 8085;
const FALLBACK_PORTS = [8086, 8087, 8088, 8089, 8090];
const MAX_RETRIES = 3;

export interface PortStatus {
  port: number;
  available: boolean;
  error?: string;
}

export interface ServerState {
  isRunning: boolean;
  port: number;
  lastCheck: Date;
  retryCount: number;
  crashed: boolean;
}

/**
 * Check if a specific port is available
 */
export async function checkPortAvailability(port: number): Promise<PortStatus> {
  try {
    // Try to fetch from the port to see if something is running
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 2000); // 2 second timeout
    
    const response = await fetch(`http://localhost:${port}`, {
      method: 'HEAD',
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);
    
    // If we get a response, the port is in use
    return {
      port,
      available: false,
      error: `Port ${port} is already in use`
    };
  } catch (error) {
    // If we get a connection error, the port is likely available
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return {
          port,
          available: false,
          error: `Port ${port} check timed out`
        };
      }
      // Connection refused usually means port is available
      if (error.message.includes('Failed to fetch')) {
        return {
          port,
          available: true
        };
      }
    }
    
    return {
      port,
      available: true
    };
  }
}

/**
 * Check if port 8085 specifically is available (not 8086 or others)
 */
export async function checkDefaultPortOnly(): Promise<PortStatus> {
  return checkPortAvailability(DEFAULT_PORT);
}

/**
 * Find the first available port from our allowed list
 */
export async function findAvailablePort(): Promise<PortStatus> {
  // Always check default port first
  const defaultStatus = await checkPortAvailability(DEFAULT_PORT);
  if (defaultStatus.available) {
    return defaultStatus;
  }

  // If default port is not available, check fallback ports
  for (const port of FALLBACK_PORTS) {
    const status = await checkPortAvailability(port);
    if (status.available) {
      return status;
    }
  }

  // No ports available
  return {
    port: DEFAULT_PORT,
    available: false,
    error: 'No available ports found in range'
  };
}

/**
 * Server state management
 */
class ServerStateManager {
  private state: ServerState = {
    isRunning: false,
    port: DEFAULT_PORT,
    lastCheck: new Date(),
    retryCount: 0,
    crashed: false
  };

  private checkInterval: number | null = null;

  /**
   * Start monitoring server state
   */
  startMonitoring(intervalMs: number = 30000): void {
    if (this.checkInterval) {
      this.stopMonitoring();
    }

    this.checkInterval = window.setInterval(() => {
      this.checkServerHealth();
    }, intervalMs);

    // Initial check
    this.checkServerHealth();
  }

  /**
   * Stop monitoring server state
   */
  stopMonitoring(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  /**
   * Check if the current server is healthy
   */
  private async checkServerHealth(): Promise<void> {
    try {
      const status = await checkPortAvailability(this.state.port);
      const wasRunning = this.state.isRunning;
      
      // If port is available but we thought server was running, it crashed
      if (status.available && wasRunning) {
        this.state.crashed = true;
        this.state.isRunning = false;
        this.state.retryCount++;
        
        console.warn(`🚨 Development server crash detected on port ${this.state.port}`);
        this.handleServerCrash();
      } else if (!status.available && !wasRunning) {
        // Server came back online
        this.state.isRunning = true;
        this.state.crashed = false;
        this.state.retryCount = 0;
        
        console.log(`✅ Development server restored on port ${this.state.port}`);
      }

      this.state.lastCheck = new Date();
    } catch (error) {
      console.error('Error checking server health:', error);
    }
  }

  /**
   * Handle server crash recovery
   */
  private async handleServerCrash(): Promise<void> {
    if (this.state.retryCount >= MAX_RETRIES) {
      console.error(`❌ Server crashed ${MAX_RETRIES} times. Manual intervention required.`);
      this.notifyUserOfCrash();
      return;
    }

    // Check if default port (8085) is still the best option
    const defaultStatus = await checkDefaultPortOnly();
    
    if (defaultStatus.available) {
      console.log(`🔄 Attempting to restart on default port ${DEFAULT_PORT}`);
      this.state.port = DEFAULT_PORT;
    } else {
      console.log(`⚠️ Default port ${DEFAULT_PORT} not available, checking alternatives`);
      const availablePort = await findAvailablePort();
      
      if (availablePort.available) {
        console.log(`🔄 Found alternative port ${availablePort.port}`);
        this.state.port = availablePort.port;
      } else {
        console.error(`❌ No available ports found for recovery`);
        this.notifyUserOfCrash();
        return;
      }
    }

    this.suggestServerRestart();
  }

  /**
   * Suggest server restart to user
   */
  private suggestServerRestart(): void {
    const message = `Development server crash detected. Please restart with: npm run dev -- --port ${this.state.port}`;
    
    // Try to show a toast notification if available
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      window.dispatchEvent(new CustomEvent('server-crash', {
        detail: {
          message,
          port: this.state.port,
          retryCount: this.state.retryCount
        }
      }));
    }

    console.log(`💡 ${message}`);
  }

  /**
   * Notify user of critical crash requiring manual intervention
   */
  private notifyUserOfCrash(): void {
    const message = `Critical: Development server has crashed ${MAX_RETRIES} times. Please check for port conflicts and restart manually.`;
    
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      window.dispatchEvent(new CustomEvent('server-critical-crash', {
        detail: {
          message,
          retryCount: this.state.retryCount,
          port: this.state.port
        }
      }));
    }

    console.error(`🚨 ${message}`);
  }

  /**
   * Get current server state
   */
  getState(): ServerState {
    return { ...this.state };
  }

  /**
   * Manually set server state (for testing or manual recovery)
   */
  setState(newState: Partial<ServerState>): void {
    this.state = { ...this.state, ...newState };
  }
}

// Export singleton instance
export const serverStateManager = new ServerStateManager();

/**
 * React hook for server state monitoring
 */
export function useServerState() {
  const [state, setState] = React.useState<ServerState>(serverStateManager.getState());

  React.useEffect(() => {
    const updateState = () => {
      setState(serverStateManager.getState());
    };

    // Listen for server crash events
    const handleServerCrash = (event: CustomEvent) => {
      console.log('Server crash detected:', event.detail);
      updateState();
    };

    const handleCriticalCrash = (event: CustomEvent) => {
      console.error('Critical server crash:', event.detail);
      updateState();
    };

    window.addEventListener('server-crash', handleServerCrash as EventListener);
    window.addEventListener('server-critical-crash', handleCriticalCrash as EventListener);

    // Start monitoring when hook is used
    serverStateManager.startMonitoring();

    // Update state periodically
    const interval = setInterval(updateState, 5000);

    return () => {
      window.removeEventListener('server-crash', handleServerCrash as EventListener);
      window.removeEventListener('server-critical-crash', handleCriticalCrash as EventListener);
      clearInterval(interval);
      serverStateManager.stopMonitoring();
    };
  }, []);

  return state;
}

/**
 * Utility to check if we're running on the preferred port
 */
export function isRunningOnPreferredPort(): boolean {
  if (typeof window === 'undefined') return false;
  
  const currentPort = window.location.port ? parseInt(window.location.port) : 
                     (window.location.protocol === 'https:' ? 443 : 80);
  
  return currentPort === DEFAULT_PORT;
}

/**
 * Get current port from window location
 */
export function getCurrentPort(): number {
  if (typeof window === 'undefined') return DEFAULT_PORT;
  
  return window.location.port ? parseInt(window.location.port) : 
         (window.location.protocol === 'https:' ? 443 : 80);
}

/**
 * Development helper: Log current port status
 */
export async function logPortStatus(): Promise<void> {
  console.group('🔌 Port Status Check');
  
  const defaultStatus = await checkDefaultPortOnly();
  console.log(`Default port ${DEFAULT_PORT}:`, defaultStatus.available ? '✅ Available' : '❌ In use');
  
  const currentPort = getCurrentPort();
  console.log(`Current port: ${currentPort}`);
  console.log(`Preferred port: ${isRunningOnPreferredPort() ? '✅ Yes' : '⚠️ No'}`);
  
  if (!defaultStatus.available) {
    console.log('Checking fallback ports...');
    for (const port of FALLBACK_PORTS) {
      const status = await checkPortAvailability(port);
      console.log(`Port ${port}:`, status.available ? '✅ Available' : '❌ In use');
    }
  }
  
  console.groupEnd();
}
