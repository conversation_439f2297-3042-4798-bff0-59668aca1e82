# Design System Guide

## Overview

The AI Question Analyzer application now features a comprehensive design system that ensures consistency, maintainability, and scalability across all components. This guide provides everything you need to know about using and extending the design system.

## Quick Start

### Basic Usage

```tsx
import { But<PERSON>, Card, useAppTheme } from '@/design-system';

function MyComponent() {
  const { getColor, getSpacing } = useAppTheme();
  
  return (
    <Card variant="elevated" padding="lg">
      <Button variant="primary" size="md">
        Click me
      </Button>
    </Card>
  );
}
```

### Theme Integration

```tsx
import { ThemeProvider, useAppTheme } from '@/design-system';

function App() {
  return (
    <ThemeProvider>
      <MyApp />
    </ThemeProvider>
  );
}

function MyApp() {
  const { mode, toggleMode, getColor } = useAppTheme();
  
  return (
    <div style={{ backgroundColor: getColor('background') }}>
      <button onClick={toggleMode}>
        Switch to {mode === 'dark' ? 'light' : 'dark'} mode
      </button>
    </div>
  );
}
```

## Design Tokens

### Colors

The design system provides a comprehensive color palette:

```tsx
// Primary colors
colors.primary[500] // Main primary color
colors.primary[600] // Hover state
colors.primary[50]  // Light background

// Semantic colors
colors.success[500] // Success actions
colors.warning[500] // Warning states
colors.error[500]   // Error states

// Analysis-specific colors
colors.analysis.multiple.bg // Multiple analysis background
colors.analysis.deep.fg     // Deep analysis foreground
```

### Typography

Consistent typography scale:

```tsx
typography.fontSize.xs    // 12px
typography.fontSize.base  // 16px
typography.fontSize['2xl'] // 24px

typography.fontWeight.normal   // 400
typography.fontWeight.semibold // 600
typography.fontWeight.bold     // 700
```

### Spacing

8px-based spacing system:

```tsx
spacing[1]  // 4px
spacing[2]  // 8px
spacing[4]  // 16px
spacing[8]  // 32px
spacing[16] // 64px
```

## Components

### Button

Comprehensive button component with multiple variants:

```tsx
// Basic usage
<Button variant="primary" size="md">
  Primary Button
</Button>

// With icons
<Button leftIcon={<PlusIcon />} variant="secondary">
  Add Item
</Button>

// Loading state
<Button loading loadingText="Saving...">
  Save
</Button>

// Icon button
<IconButton icon={<SearchIcon />} aria-label="Search" />

// Button group
<ButtonGroup>
  <Button variant="outline">Left</Button>
  <Button variant="outline">Center</Button>
  <Button variant="outline">Right</Button>
</ButtonGroup>
```

### Card

Flexible card system:

```tsx
// Basic card
<Card variant="elevated" padding="lg">
  <CardHeader title="Card Title" subtitle="Card subtitle" />
  <CardContent>
    Card content goes here
  </CardContent>
  <CardFooter>
    <Button>Action</Button>
  </CardFooter>
</Card>

// Stats card
<StatsCard
  title="Total Users"
  value="1,234"
  change={{ value: "12%", type: "increase" }}
  icon={<UsersIcon />}
/>

// Feature card
<FeatureCard
  title="Feature Name"
  description="Feature description"
  icon={<FeatureIcon />}
  action={<Button>Learn More</Button>}
/>
```

## Variants System

The design system uses a type-safe variant system:

```tsx
import { buttonVariants } from '@/design-system';

// Generate button classes
const buttonClass = buttonVariants({
  variant: 'primary',
  size: 'lg',
  fullWidth: true
});

// Custom component with variants
const MyButton = ({ variant, size, className, ...props }) => {
  return (
    <button
      className={cn(buttonVariants({ variant, size }), className)}
      {...props}
    />
  );
};
```

## Theme Customization

### Custom Colors

Add custom colors to your theme:

```tsx
// In your theme configuration
const customColors = {
  brand: {
    50: 'hsl(210, 100%, 95%)',
    500: 'hsl(210, 100%, 50%)',
    900: 'hsl(210, 100%, 20%)',
  }
};

// Use in components
const { getColor } = useAppTheme();
const brandColor = getColor('brand.500');
```

### Responsive Design

Use responsive utilities:

```tsx
import { useResponsiveValue } from '@/design-system';

function ResponsiveComponent() {
  const padding = useResponsiveValue({
    xs: '1rem',
    md: '2rem',
    lg: '3rem'
  });
  
  return <div style={{ padding }}>Responsive content</div>;
}
```

## Best Practices

### 1. Use Design Tokens

Always use design tokens instead of hardcoded values:

```tsx
// ❌ Don't do this
<div style={{ color: '#3b82f6', padding: '16px' }}>

// ✅ Do this
<div style={{ 
  color: getColor('primary.500'), 
  padding: getSpacing(4) 
}}>
```

### 2. Leverage Variants

Use the variant system for consistent styling:

```tsx
// ❌ Don't do this
<button className="bg-blue-500 text-white px-4 py-2 rounded">

// ✅ Do this
<Button variant="primary" size="md">
```

### 3. Compose Components

Build complex UIs by composing simple components:

```tsx
function UserCard({ user }) {
  return (
    <Card variant="elevated">
      <CardHeader 
        title={user.name}
        subtitle={user.email}
        action={<Button variant="ghost" size="sm">Edit</Button>}
      />
      <CardContent>
        <Text variant="body">{user.bio}</Text>
      </CardContent>
    </Card>
  );
}
```

### 4. Theme Awareness

Make components theme-aware:

```tsx
function ThemedComponent() {
  const { mode, getColor } = useAppTheme();
  
  return (
    <div 
      style={{
        backgroundColor: getColor('background'),
        color: getColor('foreground'),
        border: `1px solid ${getColor('border')}`
      }}
    >
      Current theme: {mode}
    </div>
  );
}
```

## Development Tools

### Debug Mode

Enable debug mode in development:

```tsx
import { debugDesignSystem } from '@/design-system';

// In development
if (process.env.NODE_ENV === 'development') {
  debugDesignSystem();
}
```

### Performance Monitoring

Monitor design system performance:

```tsx
import { measureDesignSystemPerformance } from '@/design-system';

const { mark, measure } = measureDesignSystemPerformance();

mark('component-render-start');
// ... component rendering
mark('component-render-end');
measure('component-render', 'component-render-start', 'component-render-end');
```

## Migration Guide

### From Old Components

Gradually migrate existing components:

```tsx
// Old component
function OldButton({ children, primary, ...props }) {
  return (
    <button 
      className={`btn ${primary ? 'btn-primary' : 'btn-secondary'}`}
      {...props}
    >
      {children}
    </button>
  );
}

// New component
function NewButton({ children, variant = 'primary', ...props }) {
  return (
    <Button variant={variant} {...props}>
      {children}
    </Button>
  );
}
```

### CSS Classes

Replace custom CSS with design system utilities:

```css
/* Old CSS */
.custom-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  padding: 24px;
}

/* New approach */
<Card variant="default" padding="lg" />
```

## Contributing

When adding new components or tokens:

1. Follow the established patterns
2. Add TypeScript types
3. Include documentation
4. Test in both light and dark modes
5. Ensure accessibility compliance

## Resources

- [Design Tokens Reference](./design-tokens.md)
- [Component API Reference](./components-api.md)
- [Theme Customization Guide](./theme-customization.md)
- [Migration Examples](./migration-examples.md)
