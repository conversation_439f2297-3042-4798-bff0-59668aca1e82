/**
 * Design System Entry Point
 * 
 * Centralized export of the entire design system.
 * This provides a single import point for all design system utilities.
 */

// Design tokens
import { designTokens } from './tokens';
export { designTokens } from './tokens';
export type { DesignTokens, ColorPalette, TypographyScale, SpacingScale } from './tokens';

// Theme system
export { ThemeProvider, useAppTheme, useResponsiveValue, createStyles } from './theme-provider';
export type { ThemeConfig, ThemeContextValue } from './theme-provider';

// Variant system
export {
  buttonVariants,
  cardVariants,
  inputVariants,
  badgeVariants,
  alertVariants,
  loadingVariants,
  textVariants,
  combineVariants,
} from './variants';

export type {
  ButtonVariants,
  CardVariants,
  InputVariants,
  BadgeVariants,
  AlertVariants,
  LoadingVariants,
  TextVariants,
} from './variants';

// Components
export * from './components';

// Utilities
export { cn } from '@/lib/utils';

// Constants
export const DESIGN_SYSTEM_VERSION = '1.0.0';

// Design system configuration
export const designSystemConfig = {
  version: DESIGN_SYSTEM_VERSION,
  prefix: 'ds',
  breakpoints: {
    xs: '475px',
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },
  animations: {
    enabled: true,
    duration: {
      fast: 150,
      normal: 300,
      slow: 500,
    },
  },
  accessibility: {
    reducedMotion: false,
    highContrast: false,
    focusVisible: true,
  },
} as const;

// Helper functions
export const getDesignToken = (path: string) => {
  const keys = path.split('.');
  let value: any = designTokens;
  
  for (const key of keys) {
    if (value && typeof value === 'object' && key in value) {
      value = value[key];
    } else {
      console.warn(`Design token path "${path}" not found`);
      return undefined;
    }
  }
  
  return value;
};

export const createComponentVariant = (
  baseClasses: string[],
  variants: Record<string, Record<string, string[]>>,
  defaultVariants?: Record<string, string>
) => {
  return (props: Record<string, string>) => {
    const classes = [...baseClasses];
    
    Object.entries(variants).forEach(([variantKey, variantOptions]) => {
      const selectedVariant = props[variantKey] || defaultVariants?.[variantKey];
      if (selectedVariant && variantOptions[selectedVariant]) {
        classes.push(...variantOptions[selectedVariant]);
      }
    });
    
    return classes.join(' ');
  };
};

// Type guards
export const isValidBreakpoint = (breakpoint: string): breakpoint is keyof typeof designSystemConfig.breakpoints => {
  return breakpoint in designSystemConfig.breakpoints;
};

export const isValidColorPath = (path: string): boolean => {
  return getDesignToken(`colors.${path}`) !== undefined;
};

export const isValidSpacing = (spacing: string): boolean => {
  return getDesignToken(`spacing.${spacing}`) !== undefined;
};

// CSS-in-JS utilities
export const createCSSVariables = (tokens: Record<string, any>, prefix = '--') => {
  const variables: Record<string, string> = {};
  
  const flatten = (obj: Record<string, any>, currentPath = '') => {
    Object.entries(obj).forEach(([key, value]) => {
      const path = currentPath ? `${currentPath}-${key}` : key;
      
      if (typeof value === 'object' && value !== null) {
        flatten(value, path);
      } else {
        variables[`${prefix}${path}`] = String(value);
      }
    });
  };
  
  flatten(tokens);
  return variables;
};

export const injectGlobalStyles = () => {
  if (typeof document === 'undefined') return;
  
  const styleId = 'design-system-global-styles';
  if (document.getElementById(styleId)) return;
  
  const style = document.createElement('style');
  style.id = styleId;
  style.textContent = `
    :root {
      ${Object.entries(createCSSVariables(designTokens))
        .map(([key, value]) => `${key}: ${value};`)
        .join('\n      ')}
    }
  `;
  
  document.head.appendChild(style);
};

// Development utilities
export const debugDesignSystem = () => {
  if (process.env.NODE_ENV !== 'development') return;
  
  console.group('🎨 Design System Debug');
  console.log('Version:', DESIGN_SYSTEM_VERSION);
  console.log('Tokens:', designTokens);
  console.log('Config:', designSystemConfig);
  console.groupEnd();
};

// Performance monitoring
export const measureDesignSystemPerformance = () => {
  if (typeof performance === 'undefined') return;
  
  const mark = (name: string) => {
    performance.mark(`design-system-${name}`);
  };
  
  const measure = (name: string, startMark: string, endMark: string) => {
    performance.measure(
      `design-system-${name}`,
      `design-system-${startMark}`,
      `design-system-${endMark}`
    );
  };
  
  return { mark, measure };
};

// Export everything as a namespace for convenience
export const DesignSystem = {
  tokens: designTokens,
  config: designSystemConfig,
  version: DESIGN_SYSTEM_VERSION,
  utils: {
    getDesignToken,
    createComponentVariant,
    createCSSVariables,
    injectGlobalStyles,
    debugDesignSystem,
    measureDesignSystemPerformance,
  },
  guards: {
    isValidBreakpoint,
    isValidColorPath,
    isValidSpacing,
  },
} as const;
