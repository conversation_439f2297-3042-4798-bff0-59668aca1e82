#!/usr/bin/env node

/**
 * Smart Development Server Starter
 * Checks port availability and manages server startup with crash recovery
 */

const { execSync, spawn } = require('child_process');
const net = require('net');

const DEFAULT_PORT = 8085;
const FALLBACK_PORTS = [8086, 8087, 8088, 8089, 8090];
const MAX_RETRIES = 3;

/**
 * Check if a port is available
 */
function checkPort(port) {
  return new Promise((resolve) => {
    const server = net.createServer();
    
    server.listen(port, (err) => {
      if (err) {
        resolve(false);
      } else {
        server.once('close', () => resolve(true));
        server.close();
      }
    });
    
    server.on('error', () => resolve(false));
  });
}

/**
 * Find the first available port
 */
async function findAvailablePort() {
  console.log(`🔍 Checking port availability...`);
  
  // Always check default port first
  const defaultAvailable = await checkPort(DEFAULT_PORT);
  if (defaultAvailable) {
    console.log(`✅ Default port ${DEFAULT_PORT} is available`);
    return DEFAULT_PORT;
  }
  
  console.log(`⚠️  Port ${DEFAULT_PORT} is in use, checking alternatives...`);
  
  // Check fallback ports
  for (const port of FALLBACK_PORTS) {
    const available = await checkPort(port);
    if (available) {
      console.log(`✅ Found available port: ${port}`);
      return port;
    }
  }
  
  throw new Error('No available ports found in range');
}

/**
 * Kill any existing processes on our target ports
 */
function killExistingProcesses() {
  const allPorts = [DEFAULT_PORT, ...FALLBACK_PORTS];
  
  for (const port of allPorts) {
    try {
      // On Windows
      if (process.platform === 'win32') {
        execSync(`netstat -ano | findstr :${port}`, { stdio: 'ignore' });
        console.log(`🔄 Found process on port ${port}, attempting to stop it...`);
        // Note: This is a basic approach. In a real scenario, you'd parse the PID and kill it
        // For development purposes, we'll just log it
      } else {
        // On Unix-like systems
        execSync(`lsof -ti:${port} | xargs kill -9`, { stdio: 'ignore' });
        console.log(`🔄 Killed existing process on port ${port}`);
      }
    } catch (error) {
      // Process not found or already stopped, which is fine
    }
  }
}

/**
 * Start the development server
 */
async function startServer() {
  let retryCount = 0;
  
  while (retryCount < MAX_RETRIES) {
    try {
      // Check for available port
      const port = await findAvailablePort();
      
      // If we're not using the default port, warn the user
      if (port !== DEFAULT_PORT) {
        console.log(`⚠️  WARNING: Starting on port ${port} instead of preferred port ${DEFAULT_PORT}`);
        console.log(`   Please ensure port ${DEFAULT_PORT} is available for optimal performance`);
      }
      
      console.log(`🚀 Starting development server on port ${port}...`);
      
      // Start the server
      const serverProcess = spawn('npm', ['run', 'dev', '--', '--port', port.toString()], {
        stdio: 'inherit',
        shell: true
      });
      
      // Handle server crash
      serverProcess.on('exit', (code) => {
        if (code !== 0) {
          console.log(`💥 Server exited with code ${code}`);
          retryCount++;
          
          if (retryCount < MAX_RETRIES) {
            console.log(`🔄 Retry ${retryCount}/${MAX_RETRIES} in 3 seconds...`);
            setTimeout(() => startServer(), 3000);
          } else {
            console.error(`❌ Server crashed ${MAX_RETRIES} times. Please check for issues and restart manually.`);
            process.exit(1);
          }
        }
      });
      
      // Handle process termination
      process.on('SIGINT', () => {
        console.log('\n🛑 Shutting down development server...');
        serverProcess.kill('SIGINT');
        process.exit(0);
      });
      
      process.on('SIGTERM', () => {
        console.log('\n🛑 Shutting down development server...');
        serverProcess.kill('SIGTERM');
        process.exit(0);
      });
      
      break; // Exit retry loop if server started successfully
      
    } catch (error) {
      console.error(`❌ Failed to start server: ${error.message}`);
      retryCount++;
      
      if (retryCount < MAX_RETRIES) {
        console.log(`🔄 Retry ${retryCount}/${MAX_RETRIES} in 5 seconds...`);
        await new Promise(resolve => setTimeout(resolve, 5000));
      } else {
        console.error(`❌ Failed to start server after ${MAX_RETRIES} attempts.`);
        console.log('💡 Try manually freeing up ports or restarting your system.');
        process.exit(1);
      }
    }
  }
}

/**
 * Main execution
 */
async function main() {
  console.log('🏗️  Smart Development Server Starter');
  console.log('=====================================');
  
  // Parse command line arguments
  const args = process.argv.slice(2);
  const forcePort = args.find(arg => arg.startsWith('--port='))?.split('=')[1];
  const killExisting = args.includes('--kill-existing');
  
  if (killExisting) {
    console.log('🧹 Cleaning up existing processes...');
    killExistingProcesses();
    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for cleanup
  }
  
  if (forcePort) {
    console.log(`🎯 Forcing port ${forcePort}`);
    // Override the default port check
    const portAvailable = await checkPort(parseInt(forcePort));
    if (!portAvailable) {
      console.error(`❌ Forced port ${forcePort} is not available`);
      process.exit(1);
    }
  }
  
  await startServer();
}

// Run the main function
if (require.main === module) {
  main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = {
  checkPort,
  findAvailablePort,
  killExistingProcesses,
  startServer
};
